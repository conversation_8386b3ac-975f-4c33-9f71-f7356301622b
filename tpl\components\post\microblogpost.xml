{var $author = $post->getOwner()}
{var $comments = $post->getLastComments(3)}
{var $commentsCount = $post->getCommentsCount()}
{var $platform = $post->getPlatform()}
{var $platformDetails = $post->getPlatformDetails()}
{var $likesCount = $post->getLikesCount()}
{var $repostsCount = $post->getRepostCount()}
{var $canBePinned = $post->canBePinnedBy($thisUser ?? NULL)}
{var $canBeDeleted = $post->canBeDeletedBy($thisUser)}
{var $feedIgnoreButton = isset($thisUser) && $post->getOwner()->getId() !== $thisUser->getId() && $post->canBeDeletedBy($thisUser)}
{var $wallOwner = $post->getWallOwner()}
{if $post->isDeactivationMessage() && $post->getText()}
    {var $deac = "post_deact"}
{else}
    {var $deac = "post_deact_silent"}
{/if}
{var $compact = isset($compact) ? $compact : false}
{var $club = isset($club) ? $club}

{var $commentTextAreaId = $post === NULL ? rand(1,300) : $post->getId()}
<div n:class="$compact ? '', !$compact ? 'page_block scroll_node'"> {* некоторые скрипты таргетят именно .scroll_node .post *}
    <div data-id="{$post->getPrettyId()}" n:class="post, $post->isExplicit() ? post-nsfw, $compact ? copy_quote">
        <div class="post_header">
            <a class="post_image{if !$post->isPostedOnBehalfOfGroup() && $author->isOnline()} online{/if}" href="{$author->getURL()}">
                {if method_exists($author, 'getOwner')}
                    <img src="{str_contains($author->getAvatarUrl('miniscule'), 'camera_200.png') 
                        ? '/themepack/vkify16/3.2.0.3/resource/community_200.png' 
                        : $author->getAvatarUrl('miniscule')}" 
                        width="50" 
                        class="post-avatar" />
                {else}
                    <img src="{$author->getAvatarURL('miniscule')}" width="50" class="post_img" />
                {/if}
            </a>
            <div class="post_header_info">
                <div class="post_author">
                    <a class="author" href="{$author->getURL()}">
                        {$author->getCanonicalName()}
                    </a>
                    <a class="page_verified" n:if="$author->isVerified()" href="/verify"></a>
                    <span class="explain">
                        {$post->isDeactivationMessage() ? 
                            ($author->isFemale() ? tr($deac . "_f") : 
                            ($author->isNeutral() ? tr($deac . "_g") : tr($deac . "_m")))}
                        {$post->isUpdateAvatarMessage() && !$post->isPostedOnBehalfOfGroup() ? 
                            ($author->isFemale() ? tr("upd_f") : 
                            ($author->isNeutral() ? tr("upd_n") : tr("upd_m")))}
                        {$post->isUpdateAvatarMessage() && $post->isPostedOnBehalfOfGroup() ? tr("upd_g") : ""}
                        {if !isset($onWallOf) && !$post->isPostedOnBehalfOfGroup() && $post->getOwnerPost() !== $post->getTargetWall()}
                            <a href="{$wallOwner->getURL()}" class="mention" data-mention-ref="{$post->getTargetWall()}">
                                {if isset($thisUser) && $thisUser->getId() === $post->getTargetWall()}
                                    {_post_on_your_wall}
                                {elseif $wallOwner instanceof \openvk\Web\Models\Entities\Club}
                                    {tr("post_on_group_wall", ovk_proc_strtr($wallOwner->getName(), 52))}
                                {else}
                                    {tr("post_on_user_wall", $wallOwner->getMorphedName("genitive", false))}
                                {/if}
                            </a>
                        {/if}
                        <span n:if="$post->isPinned()" class="wall_fixed_label">{_pinned}</span>
                    </span>
                </div>
                <div class="post_date">
                    <a href="{if !$suggestion}/wall{$post->getPrettyId()}{else}javascript:void(0){/if}" class="post_link">
                        {$post->getPublicationTime()}
                        <span n:if="$post->getEditTime()" class="edited editedMark">({_edited_short})</span>
                    </a>
                    <a n:if="!empty($platform)" class="client_app" data-app-tag="{$platform}" data-app-name="{$platformDetails['name']}" data-app-url="{$platformDetails['url']}" data-app-img="{$platformDetails['img']}">
                    <img src="/assets/packages/static/openvk/img/app_icons_mini/{$post->getPlatform(this)}.svg">
                    </a>
                    <div n:if="$post->isAd()" class="promoted_post">
                        {_post_is_ad}
                    </div>
                </div>
                <div class="post_actions" n:if="$compact == false && $thisUser">
                    <div class="post_actions_icon"></div>
                    <div class="tippy-menu">
                        {if $post->canBeEditedBy($thisUser) && !($forceNoEditLink ?? false)}
                            <a class="edit" id="editPost">{_edit}</a>
                        {/if}
                        <a n:if="$canBeDeleted && !($forceNoDeleteLink ?? false)" class="delete" href="/wall{$post->getPrettyId()}/delete">{_delete}</a>
                        {if $canBePinned && !($forceNoPinLink ?? false)}
                            {if $post->isPinned()}
                                <a class="pin" href="/wall{$post->getPrettyId()}/pin?act=unpin&hash={rawurlencode($csrfToken)}">{_unpin}</a>
                            {else}
                                <a class="pin" href="/wall{$post->getPrettyId()}/pin?act=pin&hash={rawurlencode($csrfToken)}">{_pin}</a>
                            {/if}
                        {/if}
                        {if $feedIgnoreButton}
                            <a id="__ignoreSomeoneFeed" data-val='1' data-id="{$wallOwner->getRealId()}">{if $wallOwner instanceof \openvk\Web\Models\Entities\Club}{_ignore_club}{else}{_ignore_user}{/if}</a>
                        {/if}
                        <a n:if="$thisUser->getId() != $post->getOwner()->getId()"  class="report" href="javascript:reportPost({$post->getId()})">{_report}</a>
                    </div>
                </div>
            </div>
        </div>
        <div class="post-content" id="{$post->getPrettyId()}" data-localized-nsfw-text="{_nsfw_warning}">
            <div class="text wall_text" id="text{$post->getPrettyId()}">
                <div data-text="{$post->getText(false)}" n:ifcontent class="really_text wall_post_text">
                    {if strlen($post->getText()) > 500 && !($forceNoCommentsLink ?? false)}
                        <span class="truncated_text">{$post->getText()|truncate:500|noescape}</span>
                        <span class="full_text hidden">{$post->getText()|noescape}</span>
                        <a href="javascript:void(0)" class="expand_note" onclick="toggleLongText(this)">{_show_more}</a>
                    {else}
                        {$post->getText()|noescape}
                    {/if}
                </div>

                {var $width = 514}
                {if isset($GLOBALS["_nesAttGloCou"])}
                    {var $width = $width - 70 * $GLOBALS["_nesAttGloCou"]}
                {/if}
                {var $attachmentsLayout = $post->getChildrenWithLayout($width)}
                <div n:ifcontent class="attachments attachments_b" style="height: {$attachmentsLayout->height|noescape}; width: {$attachmentsLayout->width|noescape};">
                    <div class="attachment" n:foreach="$attachmentsLayout->tiles as $attachment" style="float: {$attachment[3]|noescape}; width: {$attachment[0]|noescape}; height: {$attachment[1]|noescape};">
                        {include "../attachment.xml", attachment => $attachment[2], parent => $post, parentType => "post", tilesCount => sizeof($attachmentsLayout->tiles)}
                    </div>
                </div>

                <div n:ifcontent class="attachments attachments_m">
                    <div class="attachment" n:foreach="$attachmentsLayout->extras as $attachment">
                        {include "../attachment.xml", attachment => $attachment, post => $post}
                    </div>
                </div>

                {* Handle case when post has no text and no attachments *}
                {if !$post->getText(false) && (!$attachmentsLayout->tiles || sizeof($attachmentsLayout->tiles) == 0) && (!$attachmentsLayout->extras || sizeof($attachmentsLayout->extras) == 0)}
                    <div class="really_text wall_post_text deleted_content">
                        {include "../content_error.xml", title => '<vkifyloc name="deleted_attachment"></vkifyloc>', description => '<vkifyloc name="deleted_attachment_2"></vkifyloc>'}
                    </div>
                {/if}
            </div>
            <div n:if="$post->getGeo()" class="post-geo">
                <a onclick="javascript:openGeo({$post->getGeo()}, {$post->getTargetWall()}, {$post->getVirtualId()})">
                    <svg class="map_svg_icon" width="13" height="12" viewBox="0 0 3.4395833 3.175">
                        <g><path d="M 1.7197917 0.0025838216 C 1.1850116 0.0049444593 0.72280427 0.4971031 0.71520182 1.0190592 C 0.70756921 1.5430869 1.7223755 3.1739665 1.7223755 3.1739665 C 1.7223755 3.1739665 2.7249195 1.5439189 2.7243815 0.99632161 C 2.7238745 0.48024825 2.2492929 0.00024648357 1.7197917 0.0025838216 z M 1.7197917 0.52606608 A 0.48526123 0.48526123 0 0 1 2.2050334 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 1.4965495 A 0.48526123 0.48526123 0 0 1 1.23455 1.0113078 A 0.48526123 0.48526123 0 0 1 1.7197917 0.52606608 z " /></g>
                    </svg>
                    {$post->getGeo()->name ?? tr("admin_open")}
                </a>
            </div>
            <div n:if="$post->hasSource()" class="sourceDiv">
                <span>{_source}: {$post->getSource(true)|noescape}</span>
            </div>
            <div n:if="$post->isSigned()" class="post-signature">
                {var $actualAuthor = $post->getOwner(false)}
                <span>
                    <div class="authorIcon"></div>
                    <a href="{$actualAuthor->getURL()}" class="mention authorName" data-mention-ref="{$actualAuthor->getId()}">
                        {$actualAuthor->getCanonicalName()}
                    </a>
                </span>
            </div>
        </div>
        <div class='post_edit'></div>
        <div class="post_full_like_wrap" n:if="$thisUser && $compact == false" n:ifcontent>
            <div class="post_full_like" n:ifcontent>
                {if !($forceNoLike ?? false)}
                    {var $liked = $post->hasLikeFrom($thisUser)}
                    <a href="/wall{$post->getPrettyId()}/like?hash={rawurlencode($csrfToken)}" class="post-like-button post_like _like_wrap {if $liked}my_like{/if}" data-liked="{(int) $liked}" data-likes="{$likesCount}" data-id="{$post->getPrettyId()}" data-type='post'>
                        <i class="heart post_like_icon _icon" id="{if $liked}liked{/if}"></i>
                        <span class="post_like_link _link">{tr('mobile_like')}</span>
                        <span class="likeCnt post_like_count _count">{if $likesCount > 0}{$likesCount}{/if}</span>
                    </a>
                {/if}
                <a n:if="!($forceNoShareLink ?? false)" id="reposts{$post->getPrettyId()}" class="post-share-button post_share _share_wrap" href="javascript:repost('{$post->getPrettyId()}', 'post')">
                    <i class="repost-icon post_share_icon _icon"></i>
                    <span class="post_share_link _link">{tr('share')}</span>
                    <span class="likeCnt post_share_count _count" id="repostsCount{$post->getPrettyId()}">{if $repostsCount > 0}{$repostsCount}{/if}</span>
                </a>
            </div>
            <a n:if="!($forceNoCommentsLink ?? false) && $commentsCount == 0" class="reply_link_wrap" href="javascript:window.toggle_comment_textarea({$commentTextAreaId})">{_comment}</a>
            <div n:if="$suggestion && $canBePinned" class="suggestionControls">
                <input type="button" class="button" id="publish_post" data-id="{$post->getId()}" value="{_publish_suggested}">
                <input type="button" class="button button_gray" id="decline_post" data-id="{$post->getId()}" value="{_decline_suggested}">
            </div>
        </div>
        <div n:if="!($forceNoCommentsLink ?? false) && $commentSection == true && $compact == false" id="commentTextArea{$commentTextAreaId}" n:class="post-menu-s, ($commentsCount == 0 ? 'hidden')">
            <a n:if="$commentsCount > 3" href="/wall{$post->getPrettyId()}" class="button button_wide button_gray expand_button">{_view_other_comments}</a>
            <div n:ifcontent class="comments">
                {foreach $comments as $comment}
                    {include "../comment.xml", comment => $comment, $compact => true}
                {/foreach}
            </div>
            <div n:ifset="$thisUser" class="commentsTextFieldWrap">
                {var $commentsURL = "/al_comments/create/posts/" . $post->getId()}
                {var $club = is_null($club) ? ($post->getTargetWall() < 0 ? (new openvk\Web\Models\Repositories\Clubs)->get(abs($post->getTargetWall())) : NULL) : $club}
                {include "../textArea.xml", route => $commentsURL, postOpts => false, graffiti => (bool) ovkGetQuirk("comments.allow-graffiti"), post => $post, club => $club}
            </div>
        </div>
    </div>
</div>