{extends "../@layout.xml"}



{block title}{_my_settings}{/block}

{block content}

{var $isMain       = $mode === 'main'}
{var $isSecurity   = $mode === 'security'}
{var $isPrivacy    = $mode === 'privacy'}
{var $isFinance    = $mode === 'finance'}
{var $isFinanceTU  = $mode === 'finance.top-up'}
{var $isInterface  = $mode === 'interface'}
{var $isBl         = $mode === 'blacklist'}

{var $menuItems = [
    [
        'url' => '/settings',
        'title' => 'main',
        'active' => $isMain
    ],
    [
        'url' => '/settings?act=security',
        'title' => 'security',
        'active' => $isSecurity
    ],
    [
        'url' => '/settings?act=privacy',
        'title' => 'privacy',
        'active' => $isPrivacy
    ],
    [
        'url' => '/settings?act=blacklist',
        'title' => 'blacklist',
        'active' => $isBl
    ],
    [
        'url' => '/settings?act=finance',
        'title' => 'points',
        'active' => $isFinance || $isFinanceTU,
        'condition' => OPENVK_ROOT_CONF['openvk']['preferences']['commerce']
    ],
    [
        'url' => '/settings?act=interface',
        'title' => 'interface',
        'active' => $isInterface
    ]
]}

    <div class="wide_column_left">
        <div class="narrow_column_wrap">
            <div class="narrow_column">
                {include "../components/ui_rmenu.xml", items => $menuItems}
            </div>
        </div>
        <div class="wide_column_wrap">
            <div class="wide_column">
                    {if $isMain}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "main"}
                    <form action="/settings?act=main" method="POST" enctype="multipart/form-data">
                        <div class="page_block_sub_header">{_your_email_address}</div>
                        <div class="settings_panel clear_fix">
                            <div class="settings_list_row">
                                <div class="settings_label">{_current_email_address}</div>
                                <div class="settings_labeled_text" dir="auto">{$user->getEmail()}</div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_new_email_address}</div>
                                <div class="settings_labeled_text">
                                    <input type="email" name="new_email"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_password}</div>
                                <div class="settings_labeled_text">
                                    <input type="password" name="email_change_pass"  />
                                </div>
                            </div>
                            <div class="settings_list_row" n:if="$user->is2faEnabled()">
                                <div class="settings_label">{_"2fa_code"}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="email_change_code"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <input type="submit" value="{_save_email_address}" class="button" />
                                </div>
                            </div>
                        </div>
                        <br/>
                        <div class="page_block_sub_header">{_your_page_address}</div>
                        <div class="settings_panel clear_fix">
                            <div class="settings_list_row">
                                <div class="settings_label">{_page_id}</div>
                                <div class="settings_labeled_text" dir="auto">{$user->getId()}</div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_page_address}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="sc" value="{$user->getShortCode()}"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <input type="hidden" name="hash" value="{$csrfToken}" />
                                    <input type="submit" value="{_save}" class="button" />
                                </div>
                            </div>
                        </div>
                    </form>

                    <br/>
                    <div class="settings_block_footer">
                        {_you_can_also} <a onClick="showVkifyProfileDeactivateDialog({$csrfToken})">{_delete_your_page}</a>.
                    </div>
                    <script n:syntax="off">
                        function showVkifyProfileDeactivateDialog(hash) {
                            MessageBox(tr("profile_deactivate"), `
                                <div class="messagebox-content-header">
                                    ${tr("profile_deactivate_header")}
                                </div>
                                <form action="/settings/deactivate" method="post" id="profile_deactivate_dialog" style="margin-top: 20px">
                                    <strong>${tr("profile_deactivate_reason_header")}</strong>
                                    <div class="delete_reason_list">
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_1" data-text="${tr("profile_deactivate_reason_1_text")}"> ${tr("profile_deactivate_reason_1")}</label>
                                        </div>
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_2" data-text="${tr("profile_deactivate_reason_2_text")}"> ${tr("profile_deactivate_reason_2")}</label>
                                        </div>
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_3" data-text="${tr("profile_deactivate_reason_3_text")}"> ${tr("profile_deactivate_reason_3")}</label>
                                        </div>
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_4" data-text="${tr("profile_deactivate_reason_4_text")}"> ${tr("profile_deactivate_reason_4")}</label>
                                        </div>
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_5" data-text="${tr("profile_deactivate_reason_5_text")}"> ${tr("profile_deactivate_reason_5")}</label>
                                        </div>
                                        <div class="settings_list_row">
                                            <label><input type="radio" name="deactivate_type" id="deactivate_r_6" data-text=""> ${tr("profile_deactivate_reason_6")}</label>
                                        </div>
                                    </div>
                                    <textarea name="deactivate_reason" id="deactivate_reason" placeholder="${tr("gift_your_message")}"></textarea><br><br>
                                    <input type="checkbox" name="deactivate_share" id="deactivate_share" checked>
                                    <label for="deactivate_share">${tr("share_with_friends")}</label>
                                    <input type="hidden" name="hash" value="${hash}" />
                                </form>
                            `, [tr("profile_deactivate_button"), tr("cancel")], [
                                () => {
                                    $("#profile_deactivate_dialog").submit();
                                },
                                Function.noop
                            ]);

                            $('[id^="deactivate_r_"]').on("click", function () {
                                $('#deactivate_reason').val($(this).data("text"));
                            });
                        }
                    </script>
                </div>
                    {elseif $isSecurity}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "security"}
                    <form action="/settings?act=main" method="POST" enctype="multipart/form-data">
                        <div class="page_block_sub_header">{_change_password}</div>
                        <div class="settings_panel clear_fix">
                            <div class="settings_list_row">
                                <div class="settings_label">{_old_password}</div>
                                <div class="settings_labeled_text">
                                    <input type="password" name="old_pass"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_new_password}</div>
                                <div class="settings_labeled_text">
                                    <input type="password" name="new_pass"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_repeat_password}</div>
                                <div class="settings_labeled_text">
                                    <input type="password" name="repeat_pass"  />
                                </div>
                            </div>
                            <div class="settings_list_row" n:if="$user->is2faEnabled()">
                                <div class="settings_label">{_"2fa_code"}</div>
                                <div class="settings_labeled_text">
                                    <input type="text" name="password_change_code"  />
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <input type="hidden" name="hash" value="{$csrfToken}" />
                                    <input type="submit" value="{_change_password}" class="button" />
                                </div>
                            </div>
                        </div>
                        <br/>
                    </form>
                </div>
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "two_factor_authentication"}
                    <div class="settings_panel clear_fix settings_padding">
                        {if $user->is2faEnabled()}
                        <div class="settings_list_row">
                            <div class="accent-box">
                                {_two_factor_authentication_enabled}
                            </div>
                        </div>
                        <div class="settings_save_footer">
                            <a class="button button_gray" href="javascript:viewBackupCodes()">{_view_backup_codes}</a>
                            <a class="button button_gray" href="javascript:disableTwoFactorAuth()">{_disable}</a>
                        </div>
                        <script>
                            function viewBackupCodes() {
                                MessageBox(tr("viewing_backup_codes"), `
                                    <form id="back-codes-view-form" method="post" action="/settings/2fa">
                                        <label for="password">Пароль</label>
                                        <input type="password" id="password" name="password" required />
                                        <input type="hidden" name="hash" value={$csrfToken} />
                                    </form>
                                    `, [tr("viewing"), tr("cancel")], [
                                    () => {
                                        document.querySelector("#back-codes-view-form").submit();
                                    }, Function.noop
                                ]);
                            }

                            function disableTwoFactorAuth() {
                                MessageBox(tr("disable_2fa"), `
                                    <form id="two-factor-auth-disable-form" method="post" action="/settings/2fa/disable">
                                        <label for="password">Пароль</label>
                                        <input type="password" id="password" name="password" required />
                                        <input type="hidden" name="hash" value={$csrfToken} />
                                    </form>
                                    `, [tr("disable"), tr("cancel")], [
                                    () => {
                                        document.querySelector("#two-factor-auth-disable-form").submit();
                                    }, Function.noop
                                ]);
                            }
                        </script>
                        {else}
                        <div class="settings_list_row">
                            <div class="accent-box">
                                {_two_factor_authentication_disabled}
                            </div>
                        </div>
                        <div class="settings_save_footer">
                            <a class="button" href="/settings/2fa">{_connect}</a>
                        </div>
                        {/if}
                    </div>
                </div>
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "ui_settings_sessions"}
                    <div class="settings_panel clear_fix settings_padding">
                        <div class="settings_list_row">
                            <div class="accent-box">
                                {tr("end_all_sessions_description", OPENVK_ROOT_CONF['openvk']['appearance']['name'])}
                            </div>
                        </div>
                        <div class="settings_save_footer">
                            <form action="/revokeAllTokens" method="POST">
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_end_all_sessions}" class="button" />
                            </form>
                        </div>
                    </div>
                </div>
                {elseif $isPrivacy}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "privacy"}
                    <form action="/settings?act=privacy" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel clear_fix settings_privacy">
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_access_page}</div>
                                <div class="settings_labeled_text">
                                    <select name="page.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('page.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('page.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_read_info}</div>
                                <div class="settings_labeled_text">
                                    <select name="page.info.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('page.info.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('page.info.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('page.info.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('page.info.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_groups}</div>
                                <div class="settings_labeled_text">
                                    <select name="groups.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('groups.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('groups.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('groups.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('groups.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_photos}</div>
                                <div class="settings_labeled_text">
                                    <select name="photos.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('photos.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('photos.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('photos.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('photos.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_videos}</div>
                                <div class="settings_labeled_text">
                                    <select name="videos.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('videos.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('videos.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('videos.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('videos.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_view_audio}</div>
                                <div class="settings_labeled_text">
                                    <select name="audios.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('audios.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('audios.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('audios.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('audios.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_notes}</div>
                                <div class="settings_labeled_text">
                                    <select name="notes.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('notes.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('notes.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('notes.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('notes.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_friends}</div>
                                <div class="settings_labeled_text">
                                    <select name="friends.read" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('friends.read') == 3}selected{/if}>{_privacy_value_anybody_dative}</option>
                                        <option value="2" {if $user->getPrivacySetting('friends.read') == 2}selected{/if}>{_privacy_value_users}</option>
                                        <option value="1" {if $user->getPrivacySetting('friends.read') == 1}selected{/if}>{_privacy_value_friends_dative}</option>
                                        <option value="0" {if $user->getPrivacySetting('friends.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_add_to_friends}</div>
                                <div class="settings_labeled_text">
                                    <select name="friends.add" style="<!-- -->">
                                        <option value="3" {if $user->getPrivacySetting('friends.add') == 2}selected{/if}>{_privacy_value_anybody}</option>
                                        <option value="0" {if $user->getPrivacySetting('friends.add') == 0}selected{/if}>{_privacy_value_nobody}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_write_wall}</div>
                                <div class="settings_labeled_text">
                                    <select name="wall.write" style="<!-- -->">
                                        <option value="2" {if $user->getPrivacySetting('wall.write') == 2}selected{/if}>{_privacy_value_anybody}</option>
                                        <option value="1" {if $user->getPrivacySetting('wall.write') == 1}selected{/if}>{_privacy_value_friends}</option>
                                        <option value="0" {if $user->getPrivacySetting('wall.write') == 0}selected{/if}>{_privacy_value_only_me}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_write_messages}</div>
                                <div class="settings_labeled_text">
                                    <select name="messages.write", style="<!-- -->">
                                        <option value="2" {if $user->getPrivacySetting('messages.write') == 2}selected{/if}>{_privacy_value_anybody}</option>
                                        <option value="1" {if $user->getPrivacySetting('messages.write') == 1}selected{/if}>{_privacy_value_friends}</option>
                                        <option value="0" {if $user->getPrivacySetting('messages.write') == 0}selected{/if}>{_privacy_value_nobody}</option>
                                    </select>
                                </div>
                            </div>
                            <div n:if="OPENVK_ROOT_CONF['openvk']['preferences']['wall']['anonymousPosting']['enable']" class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_privacy_setting_see_likes}</div>
                                <div class="settings_labeled_text">
                                    <select name="likes.read", style="<!-- -->">
                                        <option value="2" {if $user->getPrivacySetting('likes.read') == 2}selected{/if}>{_privacy_value_anybody}</option>
                                        <option value="0" {if $user->getPrivacySetting('likes.read') == 0}selected{/if}>{_privacy_value_only_me_dative}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row settings_privacy_row">
                                <div class="settings_label">{_profile_type}</div>
                                <div class="settings_labeled_text">
                                    <select name="profile_type", style="<!-- -->">
                                        <option value="0" {if $user->getProfileType() == 0}selected{/if}>{_profile_type_open}</option>
                                        <option value="1" {if $user->getProfileType() == 1}selected{/if}>{_profile_type_closed}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>
                    {elseif $isFinance}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "points"}
                    <div class="settings_block_msg">
                            {presenter "openvk!Support->knowledgeBaseArticle", "points"}
                    </div>
                    <div class="settings_panel clear_fix settings_padding">
                        <div class="settings_list_row">
                            <div class="settings_label">{_on_your_account|firstUpper}:</div>
                            <div class="settings_labeled_text">
                                <b>{$thisUser->getCoins()} {_points_count}</b><br />
                                <br />
                                <div>
                                {if OPENVK_ROOT_CONF['openvk']['preferences']['ton']['enabled']}
                                <input onclick="showCoinsTopUpTroughTon()" class="button" type="submit" style="margin-right: 10px;" value="{_transfer_trough_ton}" />
                                <a href="?act=finance.top-up">
                                    <input class="button" type="submit" value="{_have_voucher}?" />
                                </a>
                                {else}
                                <a href="?act=finance.top-up">
                                    <input class="button" type="submit" value="{_have_voucher}?" />
                                    </a>
                                    {/if}
                                </div>
                            </div>
                        </div>
                    </div>
                    {script "js/node_modules/textfit/textFit.min.js"}
                    <script>
                        let balance = document.querySelector("#balance");
                        
                        balance.style.width = Math.ceil(balance.parentNode.getBoundingClientRect().width) + "px";
                        textFit(balance, { alignVert: true });

                        function convertCurrency(coin) {
                            var currencyTON = {php echo OPENVK_ROOT_CONF['openvk']['preferences']['ton']['rate']};
                            u(".coins_dialog_conv").nodes[0].innerHTML = coin * currencyTON + " TON"; 
                        }

                        function showCoinsTopUpTroughTon() {
                            MessageBox(tr("transfer_trough_ton"), `
                                <div class="messagebox-content-header">
                                    ${ tr("transfer_ton_contents") }
                                </div>
                                <div style="margin-top: 30px">` +
                                {tr("transfer_ton_address", OPENVK_ROOT_CONF['openvk']['preferences']['ton']['address'], str_replace("$1", $thisUser->getId(), OPENVK_ROOT_CONF["openvk"]["preferences"]["ton"]["hint"]))} + 
                                `<div style="text-align: center;">
                                    <img width="225" height="225" src="data:` + {$qrCodeType} + `;base64,` + {$qrCodeData} + `"><br>
                                    <input oninput="convertCurrency(this.value)"> = <span class="coins_dialog_conv">0 TON</span>
                                </div>
                                </div>`
                            , [tr("close")], [
                                Function.noop
                            ]);
                        }
                    </script>
                    <div class="settings_block_footer">
                        {tr("also_you_can_transfer_points", $thisUser->getCoins(), rawurlencode($csrfToken))|noescape}
                    </div>
                </div>
                    {elseif $isFinanceTU}
                <div class="page_block">
                    {include "../components/page_crumb_header.xml",
                        crumbs: [
                            ['title' => tr("points"), 'href' => '/settings?act=finance']
                        ],
                        current: tr("vouchers")
                    }
                    <div class="settings_block_msg">
                        {_voucher_explanation} {_voucher_explanation_ex}
                    </div>
                    <div class="settings_panel clear_fix settings_padding">
                    <form class="voucher_form" name="vouncher_form" action="/settings?act=finance.top-up" method="POST" enctype="multipart/form-data">
                        <div class="settings_voucher_input">
                            <input type="text" name="key0" class="vouncher_input" size="6" maxlength="6" placeholder="123456" required="required" oninput="autoTab(this, document.vouncher_form.key1, undefined)" style="display: inline-block; width: 50px; text-align: center;" /> -
                            <input type="text" name="key1" class="vouncher_input" size="6" maxlength="6" placeholder="789012" required="required" oninput="autoTab(this, document.vouncher_form.key2, document.vouncher_form.key0)" style="display: inline-block; width: 50px; text-align: center;" /> -
                            <input type="text" name="key2" class="vouncher_input" size="6" maxlength="6" placeholder="345678" required="required" oninput="autoTab(this, document.vouncher_form.key3, document.vouncher_form.key1)" style="display: inline-block; width: 50px; text-align: center;" /> -
                            <input type="text" name="key3" class="vouncher_input" size="6" maxlength="6" placeholder="90ABCD" required="required" oninput="autoTab(this, undefined, document.vouncher_form.key2)" style="display: inline-block; width: 50px; text-align: center;" />
                        </div>
                        <div class="settings_voucher_footer">
                            <input type="hidden" name="hash" value="{$csrfToken}" />
                            <input type="submit" value="{_redeem}" class="button" />
                        </div>
                        </form>
                    </div>
                </div>
                    {elseif $isInterface}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "interface"}
                    <form action="/settings?act=interface" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel clear_fix settings_padding">
                            <!--<div class="settings_list_row">
                                <div class="settings_label">{_avatars_style}</div>
                                <div class="settings_labeled_text">
                                    <select name="style_avatar">
                                        <option value="0" {if $user->getStyleAvatar() == 0}selected{/if}>{_arbitrary_avatars} ({_default})</option>
                                        <option value="1" {if $user->getStyleAvatar() == 1}selected{/if}>{_cut}</option>
                                        <option value="2" {if $user->getStyleAvatar() == 2}selected{/if}>{_round_avatars}</option>
                                    </select>
                                </div>
                            </div>-->
                            <div class="settings_list_row">
                                <div class="settings_label">{_style}</div>
                                <div class="settings_labeled_text">
                                    <select name="style">
                                        <option value="ovk" {if $user->getStyle() == 'ovk'}selected{/if}>OpenVK ({_default})</option>
                                        <option n:foreach="$themes as $id => $theme"
                                                n:attr="selected => $user->getStyle() === $id"
                                                value="{$id}">
                                                    {$theme}
                                        </option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="theme_mode" value="1"> <vkifyloc name="dark_mode"></vkifyloc></label>
                                </div>
                            <script>
                                function initDarkModeToggle() {
                                    const checkbox = document.querySelector('input[name="theme_mode"]');
                                    const isDarkMode = localStorage.getItem('vkify.darkmode') === '1';

                                    checkbox.checked = isDarkMode;

                                    if (window.toggleDarkMode) {
                                        window.toggleDarkMode(isDarkMode);
                                    }

                                    checkbox.onchange = function() {
                                        const enabled = this.checked;
                                        localStorage.setItem('vkify.darkmode', enabled ? '1' : '0');
                                        if (window.toggleDarkMode) {
                                            window.toggleDarkMode(enabled);
                                        }
                                    };
                                }

                                if (window.toggleDarkMode) {
                                    initDarkModeToggle();
                                } else {
                                    window.addEventListener('load', initDarkModeToggle);
                                }
                            </script>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">{_ui_settings_nsfw_content}</div>
                                <div class="settings_labeled_text">
                                    <select name="nsfw">
                                        <option value="0" {if $user->getNsfwTolerance() === 0}selected{/if}>{_ui_settings_nsfw_content_dont_show}</option>
                                        <option value="1" {if $user->getNsfwTolerance() === 1}selected{/if}>{_ui_settings_nsfw_content_blur}</option>
                                        <option value="2" {if $user->getNsfwTolerance() === 2}selected{/if}>{_ui_settings_nsfw_content_show}</option>
                                    </select>
                                </div>
                            </div>
                            <!--<div class="settings_list_row">
                                <div class="settings_label">{_ui_settings_view_of_posts}</div>
                                <div class="settings_labeled_text">
                                    <select name="microblog" disabled>
                                        <option value="1" selected>{_ui_settings_view_of_posts_microblog}</option>
                                    </select>
                                    <p><vkifyloc name="oldposts_disabled_desc"></vkifyloc></p>
                                </div>
                            </div>-->
                            <div class="settings_list_row">
                                <div class="settings_label">{_ui_settings_main_page}</div>
                                <div class="settings_labeled_text">
                                    <select name="main_page">
                                        <option value="0" {if !$user->getMainPage()}selected{/if}>{_my_page}</option>
                                        <option value="1" {if $user->getMainPage()}selected{/if}>{_my_feed}</option>
                                    </select>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="theme_for_session" value="1"> {_apply_style_for_this_device}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label>
                                        <input class="hidden" type="hidden" name="rating" value="0">
                                        <input type="checkbox" name="rating" value="1" n:attr="checked => !$user->prefersNotToSeeRating()"> <vkifyloc name="profile_recommendations"/>
                                    </label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="vkgraffiti" value="0"> <vkifyloc name="graffitiswitch"></vkifyloc></label>
                                </div>
                            <script>
                                document.querySelector('input[name="vkgraffiti"]').checked = localStorage.getItem('vkify.graffitiType') === '1';
                                document.querySelector('input[name="vkgraffiti"]').onchange = () => localStorage.setItem('vkify.graffitiType', document.querySelector('input[name="vkgraffiti"]').checked ? '1' : '0');
                            </script>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="newhat" value="0"> <vkifyloc name="ovkhat"></vkifyloc></label>
                                </div>
                            <script>
                                document.querySelector('input[name="newhat"]').checked = document.cookie.includes('newhat=realno_novaya_shapochka');
                                document.querySelector('input[name="newhat"]').onchange = () => {
                                    document.cookie = `newhat=${ document.querySelector('input[name="newhat"]').checked ? 'realno_novaya_shapochka' : '' }; path=/; max-age=31536000`;
                                };
                            </script>
                            </div>
                            <div class="settings_list_row" id="_js_settings">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text">
                                    <a href="javascript:openVkifyJsSettings()">{_ui_settings_window}</a>
                                </div>
                            </div>
                            <script n:syntax="off">
                                function openVkifyJsSettings() {
                                    const CURRENT_AUTO_SCROLL = Number(localStorage.getItem('ux.auto_scroll') ?? 1)
                                    const CURRENT_DISABLE_AJAX = Number(localStorage.getItem('ux.disable_ajax_routing') ?? 0)

                                    u("#_js_settings .js_settings_container").remove()
                                    u("#_js_settings").append(`
                                        <div class="js_settings_container">
                                            <div class="settings_list_row">
                                                <div class="settings_label"></div>
                                                <div class="settings_labeled_text">
                                                    <label>
                                                        <input type='checkbox' data-act='localstorage_item' data-inverse="1" name='ux.disable_ajax_routing' id="ux.disable_ajax_routing" ${CURRENT_DISABLE_AJAX == 0 ? 'checked' : ''}>
                                                        ${tr('ajax_routing')}
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="settings_list_row">
                                                <div class="settings_label"></div>
                                                <div class="settings_labeled_text">
                                                    <label>
                                                    <input type='checkbox' data-act='localstorage_item' name='ux.auto_scroll' id="ux.auto_scroll" ${CURRENT_AUTO_SCROLL == 1 ? 'checked' : ''}>
                                                        ${tr('auto_scroll')}
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    `)
                                }
                            </script>
                            <div class="settings_list_row">
                                <div class="settings_label"></div>
                                <div class="settings_labeled_text">
                                    <input type="hidden" name="hash" value="{$csrfToken}" />
                                    <input type="submit" value="{_save}" class="button" />
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "ui_settings_sidebar"}
                    <form action="/settings?act=lMenu" method="POST" enctype="multipart/form-data">
                        <div class="settings_panel clear_fix settings_padding">
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_bildoj" n:attr="checked => $user->getLeftMenuItemStatus('photos')"> {_my_photos}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_filmetoj" n:attr="checked => $user->getLeftMenuItemStatus('videos')"> {_my_videos}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_muziko" n:attr="checked => $user->getLeftMenuItemStatus('audios')"> {_my_audios}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_mesagoj" n:attr="checked => $user->getLeftMenuItemStatus('messages')"> {_my_messages}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_notatoj" n:attr="checked => $user->getLeftMenuItemStatus('notes')"> {_my_notes}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_grupoj" n:attr="checked => $user->getLeftMenuItemStatus('groups')"> {_my_groups}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_novajoj" n:attr="checked => $user->getLeftMenuItemStatus('news')"> {_my_feed}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_aplikoj" n:attr="checked => $user->getLeftMenuItemStatus('apps')"> {_my_apps}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_doxc" n:attr="checked => $user->getLeftMenuItemStatus('docs')"> {_my_documents}</label>
                                </div>
                            </div>
                            <div class="settings_list_row">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_feva" n:attr="checked => $user->getLeftMenuItemStatus('fave')"> {_bookmarks_tab}</label>
                                </div>
                            </div>
                            <div class="settings_list_row" n:if="sizeof(OPENVK_ROOT_CONF['openvk']['preferences']['menu']['links']) > 0">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_ligiloj" n:attr="checked => $user->getLeftMenuItemStatus('links')"> {_additional_links}</label>
                                </div>
                            </div>
                            <div class="settings_list_row" n:if="OPENVK_ROOT_CONF['openvk']['preferences']['adPoster']['enable']">
                                <div class="settings_label">&nbsp;</div>
                                <div class="settings_labeled_text" dir="auto">
                                    <label><input type="checkbox" name="menu_standardo" n:attr="checked => $user->getLeftMenuItemStatus('poster')"> {_ad_poster}</label>
                                </div>
                            </div>
                            <div class="settings_save_footer">
                                <input type="hidden" name="hash" value="{$csrfToken}" />
                                <input type="submit" value="{_save}" class="button" />
                            </div>
                        </div>
                    </form>
                </div>
                    {elseif $isBl}
                <div class="page_block">
                    {include "../components/page_block_header.xml", title => "blacklist", count => $blSize}
                        {if $blSize < 1}
                            {include "../components/error.xml", description => tr("bl_count_zero_desc")}
                        {else}
                            <div class="list_view">
                                <div n:foreach="$blItems as $item" class="search_row">
                                    <a href="{$item->getURL()}" class="img">
                                        <img src='{$item->getAvatarURL()}'>
                                    </a>
                                    <div class="info">
                                        <div class="labeled name">
                                            <a href="{$item->getURL()}">
                                                {$item->getCanonicalName()} 
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {include "../components/paginator.xml", conf => $paginatorConf}
                            </div>
                        {/if}
                    {/if}
                </div>
            </div>
        </div>
    </div>
{/block}
